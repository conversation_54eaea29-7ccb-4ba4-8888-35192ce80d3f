package com.aiwardenmod.init;

import com.aiwardenmod.AIWardenMod;
import com.aiwardenmod.entity.AIWardenEntity;
import net.minecraft.entity.EntityClassification;
import net.minecraft.entity.EntityType;
import net.minecraftforge.event.entity.EntityAttributeCreationEvent;
import net.minecraftforge.fml.RegistryObject;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;

public class ModEntities {
    public static final DeferredRegister<EntityType<?>> ENTITIES = DeferredRegister.create(ForgeRegistries.ENTITIES, AIWardenMod.MOD_ID);

    public static final RegistryObject<EntityType<AIWardenEntity>> AI_WARDEN = ENTITIES.register("ai_warden",
            () -> EntityType.Builder.of(AIWardenEntity::new, EntityClassification.MONSTER)
                    .sized(0.9F, 2.9F)
                    .clientTrackingRange(16)
                    .updateInterval(2)
                    .build("ai_warden"));

    public static void registerEntityAttributes(EntityAttributeCreationEvent event) {
        event.put(AI_WARDEN.get(), AIWardenEntity.createAttributes().build());
    }
}
