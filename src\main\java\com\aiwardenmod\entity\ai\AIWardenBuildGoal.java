package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.AIWardenEntity;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.Goal;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;

import java.util.EnumSet;

public class AIWardenBuildGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int buildCooldown = 0;
    private int maxBuildHeight = 5;

    public AIWardenBuildGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        if (this.buildCooldown > 0) {
            this.buildCooldown--;
            return false;
        }

        this.target = this.warden.getTarget();
        if (this.target == null) {
            return false;
        }

        // Check if target is significantly higher than the warden
        double heightDifference = this.target.getY() - this.warden.getY();
        if (heightDifference < 2.0D) {
            return false;
        }

        // Check if warden can't reach the target normally
        double distance = this.warden.distanceTo(this.target);
        return distance > 3.0D && distance < 16.0D && heightDifference > 1.5D;
    }

    @Override
    public boolean canContinueToUse() {
        return this.target != null && this.target.isAlive() && this.canUse();
    }

    @Override
    public void start() {
        this.warden.getNavigation().stop();
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Try to build a path to the target
        if (this.warden.getRandom().nextInt(20) == 0) {
            this.tryBuildPath();
        }
    }

    private void tryBuildPath() {
        World world = this.warden.level;
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction towards target
        int deltaX = Integer.compare(targetPos.getX(), wardenPos.getX());
        int deltaZ = Integer.compare(targetPos.getZ(), wardenPos.getZ());

        // Try to build blocks in the direction of the target
        for (int height = 1; height <= this.maxBuildHeight; height++) {
            BlockPos buildPos = wardenPos.offset(deltaX, height, deltaZ);

            // Check if we can place a block here
            if (this.canPlaceBlock(world, buildPos)) {
                // Place a cobblestone block
                world.setBlock(buildPos, Blocks.COBBLESTONE.defaultBlockState(), 3);

                // Set cooldown to prevent spam building
                this.buildCooldown = 40;
                break;
            }
        }

        // Also try building a platform around the warden for better positioning
        if (this.warden.getRandom().nextInt(40) == 0) {
            this.buildPlatform();
        }
    }

    private void buildPlatform() {
        World world = this.warden.level;
        BlockPos wardenPos = this.warden.blockPosition();

        // Build a small platform around the warden
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue; // Don't build under the warden itself

                BlockPos buildPos = wardenPos.offset(x, 0, z);
                if (this.canPlaceBlock(world, buildPos)) {
                    world.setBlock(buildPos, Blocks.COBBLESTONE.defaultBlockState(), 3);
                }
            }
        }
    }

    private boolean canPlaceBlock(World world, BlockPos pos) {
        BlockState currentState = world.getBlockState(pos);

        // Don't replace solid blocks or important blocks
        if (!currentState.getBlock().isAir(currentState, world, pos) && !currentState.getMaterial().isReplaceable()) {
            return false;
        }

        // Don't build too high
        if (pos.getY() > this.warden.getY() + this.maxBuildHeight) {
            return false;
        }

        // Don't build in water or lava
        if (currentState.getBlock() == Blocks.WATER || currentState.getBlock() == Blocks.LAVA) {
            return false;
        }

        // Make sure there's a solid block below (or it's the ground level)
        BlockPos below = pos.below();
        BlockState belowState = world.getBlockState(below);

        return belowState.isSolidRender(world, below) || pos.getY() <= this.warden.getY();
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildCooldown = 60; // Longer cooldown when stopping
    }
}
