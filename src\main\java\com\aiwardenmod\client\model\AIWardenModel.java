package com.aiwardenmod.client.model;

import com.aiwardenmod.entity.AIWardenEntity;
import com.google.common.collect.ImmutableList;
import net.minecraft.client.renderer.entity.model.SegmentedModel;
import net.minecraft.client.renderer.model.ModelRenderer;
import net.minecraft.util.math.MathHelper;

public class AIWardenModel<T extends AIWardenEntity> extends SegmentedModel<T> {
    private final ModelRenderer head;
    private final ModelRenderer body;
    private final ModelRenderer rightArm;
    private final ModelRenderer leftArm;
    private final ModelRenderer rightLeg;
    private final ModelRenderer leftLeg;

    public AIWardenModel() {
        this.texWidth = 128;
        this.texHeight = 128;

        this.head = new ModelRenderer(this, 0, 0);
        this.head.addBox(-4.0F, -12.0F, -5.5F, 8.0F, 10.0F, 8.0F, 0.0F);
        this.head.setPos(0.0F, -7.0F, -2.0F);

        this.body = new ModelRenderer(this, 0, 40);
        this.body.addBox(-9.0F, -2.0F, -6.0F, 18.0F, 12.0F, 11.0F, 0.0F);
        this.body.setPos(0.0F, -7.0F, 0.0F);

        this.rightArm = new ModelRenderer(this, 60, 21);
        this.rightArm.addBox(-13.0F, -2.5F, -3.0F, 4.0F, 30.0F, 6.0F, 0.0F);
        this.rightArm.setPos(0.0F, -7.0F, 0.0F);

        this.leftArm = new ModelRenderer(this, 60, 58);
        this.leftArm.addBox(9.0F, -2.5F, -3.0F, 4.0F, 30.0F, 6.0F, 0.0F);
        this.leftArm.setPos(0.0F, -7.0F, 0.0F);

        this.rightLeg = new ModelRenderer(this, 37, 0);
        this.rightLeg.addBox(-3.5F, -3.0F, -3.0F, 6.0F, 16.0F, 5.0F, 0.0F);
        this.rightLeg.setPos(-4.0F, 11.0F, 0.0F);

        this.leftLeg = new ModelRenderer(this, 60, 0);
        this.leftLeg.addBox(-3.5F, -3.0F, -3.0F, 6.0F, 16.0F, 5.0F, 0.0F);
        this.leftLeg.setPos(5.0F, 11.0F, 0.0F);
    }

    @Override
    public Iterable<ModelRenderer> parts() {
        return ImmutableList.of(this.head, this.body, this.leftArm, this.rightArm, this.leftLeg, this.rightLeg);
    }

    @Override
    public void setupAnim(T entityIn, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch) {
        this.head.yRot = netHeadYaw * ((float)Math.PI / 180F);
        this.head.xRot = headPitch * ((float)Math.PI / 180F);
        
        this.rightLeg.xRot = -1.5F * MathHelper.triangleWave(limbSwing, 13.0F) * limbSwingAmount;
        this.leftLeg.xRot = 1.5F * MathHelper.triangleWave(limbSwing, 13.0F) * limbSwingAmount;
        this.rightLeg.yRot = 0.0F;
        this.leftLeg.yRot = 0.0F;
        
        this.rightArm.xRot = -0.75F * MathHelper.triangleWave(limbSwing, 13.0F) * limbSwingAmount;
        this.leftArm.xRot = 0.75F * MathHelper.triangleWave(limbSwing, 13.0F) * limbSwingAmount;
    }
}
