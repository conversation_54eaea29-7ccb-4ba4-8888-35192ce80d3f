package com.aiwardenmod.client.renderer;

import com.aiwardenmod.client.model.AIWardenModel;
import com.aiwardenmod.entity.AIWardenEntity;
import net.minecraft.client.renderer.entity.EntityRendererManager;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.util.ResourceLocation;

public class AI<PERSON>ardenRenderer extends <PERSON><PERSON><PERSON><PERSON><PERSON><AIWardenEntity, AIWardenModel<AIWardenEntity>> {
    private static final ResourceLocation TEXTURE = new ResourceLocation("aiwardenmod", "textures/entity/ai_warden.png");

    public AIWardenRenderer(EntityRendererManager renderManagerIn) {
        super(renderManagerIn, new AIWardenModel<>(), 0.9F);
    }

    @Override
    public ResourceLocation getTextureLocation(AIWardenEntity entity) {
        return TEXTURE;
    }
}
