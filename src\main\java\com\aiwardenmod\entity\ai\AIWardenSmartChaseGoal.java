package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.AIWardenEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.Goal;
import net.minecraft.pathfinding.Path;

import net.minecraft.util.math.vector.Vector3d;

import java.util.EnumSet;

public class AIWardenSmartChaseGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private final boolean followingEvenIfNotSeen;
    private Path path;
    private double pathedTargetX;
    private double pathedTargetY;
    private double pathedTargetZ;
    private int ticksUntilNextPathRecalculation;
    private int ticksUntilNextAttack;
    private long lastCanUseCheck;
    private boolean canPenalize = false;

    public AIWardenSmartChaseGoal(AIWardenEntity warden, double speedModifier, boolean followingEvenIfNotSeen) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.followingEvenIfNotSeen = followingEvenIfNotSeen;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        long i = this.warden.level.getGameTime();
        if (i - this.lastCanUseCheck < 20L) {
            return false;
        } else {
            this.lastCanUseCheck = i;
            LivingEntity target = this.warden.getTarget();
            if (target == null) {
                return false;
            } else if (!target.isAlive()) {
                return false;
            } else {
                if (canPenalize) {
                    if (--this.ticksUntilNextPathRecalculation <= 0) {
                        this.path = this.warden.getNavigation().createPath(target, 0);
                        this.ticksUntilNextPathRecalculation = 4 + this.warden.getRandom().nextInt(7);
                        return this.path != null;
                    } else {
                        return true;
                    }
                }
                this.path = this.warden.getNavigation().createPath(target, 0);
                if (this.path != null) {
                    return true;
                } else {
                    return this.getAttackReachSqr(target) >= this.warden.distanceToSqr(target.getX(), target.getY(), target.getZ());
                }
            }
        }
    }

    @Override
    public boolean canContinueToUse() {
        LivingEntity target = this.warden.getTarget();
        if (target == null) {
            return false;
        } else if (!target.isAlive()) {
            return false;
        } else if (!this.followingEvenIfNotSeen) {
            return !this.warden.getNavigation().isDone();
        } else if (!this.warden.isWithinRestriction(target.blockPosition())) {
            return false;
        } else {
            return !(target instanceof net.minecraft.entity.player.PlayerEntity) || !target.isSpectator() && !((net.minecraft.entity.player.PlayerEntity)target).isCreative();
        }
    }

    @Override
    public void start() {
        this.warden.getNavigation().moveTo(this.path, this.speedModifier);
        this.warden.setAggressive(true);
        this.ticksUntilNextPathRecalculation = 0;
        this.ticksUntilNextAttack = 0;
    }

    @Override
    public void stop() {
        LivingEntity target = this.warden.getTarget();
        if (target instanceof net.minecraft.entity.player.PlayerEntity) {
            net.minecraft.entity.player.PlayerEntity player = (net.minecraft.entity.player.PlayerEntity) target;
            if (player.isCreative() || player.isSpectator()) {
                this.warden.setTarget(null);
            }
        }
        this.warden.setAggressive(false);
        this.warden.getNavigation().stop();
    }

    @Override
    public void tick() {
        LivingEntity target = this.warden.getTarget();
        if (target != null) {
            this.warden.getLookControl().setLookAt(target, 30.0F, 30.0F);
            double distanceSqr = this.warden.distanceToSqr(target.getX(), target.getY(), target.getZ());
            this.ticksUntilNextPathRecalculation = Math.max(this.ticksUntilNextPathRecalculation - 1, 0);

            // Predictive movement - try to predict where the target will be
            Vector3d targetVelocity = target.getDeltaMovement();
            double predictedX = target.getX() + targetVelocity.x * 10;
            double predictedY = target.getY() + targetVelocity.y * 10;
            double predictedZ = target.getZ() + targetVelocity.z * 10;

            if ((this.followingEvenIfNotSeen || this.warden.getSensing().canSee(target)) && this.ticksUntilNextPathRecalculation <= 0 && (this.pathedTargetX == 0.0D && this.pathedTargetY == 0.0D && this.pathedTargetZ == 0.0D || target.distanceToSqr(this.pathedTargetX, this.pathedTargetY, this.pathedTargetZ) >= 1.0D || this.warden.getRandom().nextFloat() < 0.05F)) {
                this.pathedTargetX = predictedX;
                this.pathedTargetY = predictedY;
                this.pathedTargetZ = predictedZ;
                this.ticksUntilNextPathRecalculation = 4 + this.warden.getRandom().nextInt(7);

                if (distanceSqr > 1024.0D) {
                    this.ticksUntilNextPathRecalculation += 10;
                } else if (distanceSqr > 256.0D) {
                    this.ticksUntilNextPathRecalculation += 5;
                }

                if (!this.warden.getNavigation().moveTo(predictedX, predictedY, predictedZ, this.speedModifier)) {
                    this.ticksUntilNextPathRecalculation += 15;
                }
            }

            this.ticksUntilNextAttack = Math.max(this.ticksUntilNextAttack - 1, 0);
            this.checkAndPerformAttack(target, distanceSqr);
        }
    }

    protected void checkAndPerformAttack(LivingEntity target, double distanceSqr) {
        double attackReach = this.getAttackReachSqr(target);
        if (distanceSqr <= attackReach && this.ticksUntilNextAttack <= 0) {
            this.resetAttackCooldown();
            this.warden.swing(net.minecraft.util.Hand.MAIN_HAND);
            this.warden.doHurtTarget(target);
        }
    }

    protected void resetAttackCooldown() {
        this.ticksUntilNextAttack = 20;
    }

    protected double getAttackReachSqr(LivingEntity target) {
        return this.warden.getBbWidth() * 2.0F * this.warden.getBbWidth() * 2.0F + target.getBbWidth();
    }
}
