package com.aiwardenmod.init;

import com.aiwardenmod.AIWardenMod;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroup;
import net.minecraftforge.common.ForgeSpawnEggItem;
import net.minecraftforge.fml.RegistryObject;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;

public class ModItems {
    public static final DeferredRegister<Item> ITEMS = DeferredRegister.create(ForgeRegistries.ITEMS, AIWardenMod.MOD_ID);

    public static final RegistryObject<ForgeSpawnEggItem> AI_WARDEN_SPAWN_EGG = ITEMS.register("ai_warden_spawn_egg",
            () -> new ForgeSpawnEggItem(ModEntities.AI_WARDEN, 0x0F1419, 0x42FFF5,
                    new Item.Properties().tab(ItemGroup.TAB_MISC)));
}
