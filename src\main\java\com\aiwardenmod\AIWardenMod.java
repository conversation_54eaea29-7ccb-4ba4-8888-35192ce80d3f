package com.aiwardenmod;

import com.aiwardenmod.init.ModEntities;
import com.aiwardenmod.init.ModItems;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.EntityAttributeCreationEvent;
import net.minecraftforge.eventbus.api.IEventBus;

import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod(AIWardenMod.MOD_ID)
public class AIWardenMod {
    public static final String MOD_ID = "aiwardenmod";
    private static final Logger LOGGER = LogManager.getLogger();

    public AIWardenMod() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();

        // Register mod content
        ModEntities.ENTITIES.register(modEventBus);
        ModItems.ITEMS.register(modEventBus);

        // Register event listeners
        modEventBus.addListener(this::setup);
        modEventBus.addListener(this::doClientStuff);
        modEventBus.addListener(this::entityAttributes);

        // Register ourselves for server and other game events
        MinecraftForge.EVENT_BUS.register(this);
    }

    private void setup(final FMLCommonSetupEvent event) {
        LOGGER.info("AI Warden Mod setup complete!");
    }

    private void entityAttributes(final EntityAttributeCreationEvent event) {
        ModEntities.registerEntityAttributes(event);
    }

    private void doClientStuff(final FMLClientSetupEvent event) {
        LOGGER.info("AI Warden Mod client setup complete!");
    }
}
