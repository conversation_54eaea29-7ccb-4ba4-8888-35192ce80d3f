package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.AIWardenEntity;
import net.minecraft.block.BlockState;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.Goal;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.vector.Vector3d;
import net.minecraft.world.World;

import java.util.EnumSet;

public class AIWardenJumpGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int jumpCooldown = 0;
    private int jumpChargeTime = 0;
    private boolean isCharging = false;

    public AIWardenJumpGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.JUMP));
    }

    @Override
    public boolean canUse() {
        if (this.jumpCooldown > 0) {
            this.jumpCooldown--;
            return false;
        }

        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Check if target is at a different height and within jumping range
        double distance = this.warden.distanceTo(this.target);
        double heightDifference = this.target.getY() - this.warden.getY();

        // Jump if target is higher and within range, or if there's an obstacle
        return (distance < 8.0D && distance > 2.0D) &&
               (heightDifference > 1.0D || this.hasObstacleInPath());
    }

    @Override
    public boolean canContinueToUse() {
        return this.target != null && this.target.isAlive() &&
               (this.isCharging || this.canUse());
    }

    @Override
    public void start() {
        this.isCharging = true;
        this.jumpChargeTime = 20; // 1 second charge time
        this.warden.getNavigation().stop();
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        if (this.isCharging) {
            this.jumpChargeTime--;

            // Visual effect - maybe add particles here in the future
            if (this.jumpChargeTime <= 0) {
                this.performJump();
                this.isCharging = false;
            }
        }
    }

    private void performJump() {
        Vector3d targetPos = this.target.position();
        Vector3d wardenPos = this.warden.position();

        // Calculate jump vector
        double deltaX = targetPos.x - wardenPos.x;
        double deltaY = targetPos.y - wardenPos.y;
        double deltaZ = targetPos.z - wardenPos.z;

        // Normalize horizontal distance
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (horizontalDistance > 0.1D) {
            // Calculate jump strength based on distance
            double jumpStrength = Math.min(horizontalDistance * 0.3D, 1.5D);
            double upwardForce = Math.max(0.5D, deltaY * 0.2D + 0.3D);

            // Apply jump motion
            Vector3d jumpMotion = new Vector3d(
                (deltaX / horizontalDistance) * jumpStrength,
                upwardForce,
                (deltaZ / horizontalDistance) * jumpStrength
            );

            this.warden.setDeltaMovement(jumpMotion);
            this.warden.hasImpulse = true;

            // Set cooldown
            this.jumpCooldown = 60; // 3 seconds
        }
    }

    private boolean hasObstacleInPath() {
        if (this.target == null) {
            return false;
        }

        World world = this.warden.level;
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Check if there are blocks blocking the direct path
        Vector3d direction = new Vector3d(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Check a few blocks ahead
        for (int i = 1; i <= 3; i++) {
            BlockPos checkPos = wardenPos.offset(
                (int)(direction.x * i),
                1,
                (int)(direction.z * i)
            );

            BlockState blockState = world.getBlockState(checkPos);
            if (!blockState.getBlock().isAir(blockState, world, checkPos) && blockState.isSolidRender(world, checkPos)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void stop() {
        this.target = null;
        this.isCharging = false;
        this.jumpChargeTime = 0;
        this.jumpCooldown = 40; // Cooldown when stopping
    }
}
