# AI Warden Mod for Minecraft 1.16.5

## Overview
This mod adds an enhanced AI Warden entity that is significantly more intelligent than the vanilla Warden. The AI Warden features advanced pathfinding, block building capabilities, and jumping mechanics to chase players more effectively.

## Features

### AI Warden Entity
- **Enhanced Health**: 500 HP (compared to vanilla Warden's 500 HP)
- **Increased Attack Damage**: 30 damage per hit
- **Smart Movement**: 0.3 movement speed with advanced pathfinding
- **Knockback Resistance**: Complete immunity to knockback

### Advanced AI Behaviors

#### 1. Smart Chase Goal
- **Predictive Movement**: Anticipates player movement and paths to where the player will be
- **Adaptive Pathfinding**: Recalculates paths based on player behavior
- **Enhanced Tracking**: Better target acquisition and following

#### 2. Block Building AI
- **Intelligent Construction**: Places cobblestone blocks to reach higher areas
- **Platform Building**: Creates platforms for better positioning
- **Height Awareness**: Builds up to 5 blocks high to reach targets
- **Cooldown System**: Prevents spam building (40-60 tick cooldowns)

#### 3. Jump Mechanics
- **Obstacle Navigation**: Jumps over blocks and terrain
- **Predictive Jumping**: Calculates jump trajectories to reach targets
- **Charge System**: 1-second charge time before jumping for balanced gameplay
- **Smart Targeting**: Only jumps when beneficial for reaching the target

### Items

#### AI Warden Spawn Egg
- **Name**: "AI Warden Spawn Egg"
- **Colors**: Dark blue (0x0F1419) with cyan highlights (0x42FFF5)
- **Creative Tab**: Miscellaneous
- **Crafting Recipe**: 
  ```
  S E S
  E W E
  S E S
  ```
  Where:
  - S = Sculk Block
  - E = Echo Shard
  - W = Warden Spawn Egg (if available)

## Installation

1. **Prerequisites**:
   - Minecraft 1.16.5
   - Minecraft Forge 36.2.42 or compatible version

2. **Building the Mod**:
   ```bash
   ./gradlew build
   ```

3. **Installation**:
   - Copy the generated JAR file from `build/libs/` to your Minecraft `mods` folder
   - Launch Minecraft with Forge

## Usage

1. **Spawning AI Wardens**:
   - Use the AI Warden Spawn Egg in Creative mode
   - Craft the spawn egg using the recipe above
   - AI Wardens will naturally exhibit their enhanced behaviors

2. **Observing AI Behaviors**:
   - **Building**: Watch as the AI Warden places blocks to reach you
   - **Jumping**: See it jump over obstacles and terrain
   - **Smart Chasing**: Notice how it predicts your movement

## Technical Details

### Entity Properties
- **Entity ID**: `aiwardenmod:ai_warden`
- **Classification**: Monster
- **Size**: 0.9F width × 2.9F height
- **Tracking Range**: 16 blocks
- **Update Interval**: 2 ticks

### AI Goal Priorities
1. Swimming (Priority 0)
2. Jumping (Priority 1)
3. Building (Priority 2)
4. Smart Chasing (Priority 3)
5. Melee Attack (Priority 4)
6. Random Walking (Priority 5)
7. Look at Player (Priority 6)
8. Look Randomly (Priority 7)

### Loot Drops
- **Sculk Catalyst**: 1-2 items (affected by Looting)
- **Echo Shard**: 1-3 items (affected by Looting)

## Configuration Notes

### Texture
- Place a custom texture at `src/main/resources/assets/aiwardenmod/textures/entity/ai_warden.png`
- Recommended size: 128×128 pixels
- You can copy and modify the vanilla Warden texture for a custom look

### Balancing
The AI Warden is designed to be challenging but fair:
- Building has cooldowns to prevent overwhelming block placement
- Jumping requires charging time
- Predictive movement is balanced to be smart but not unfair

## Compatibility
- **Minecraft Version**: 1.16.5
- **Forge Version**: 36.2.42+
- **Java Version**: 8+

## Known Issues
- The AI Warden uses a simplified model based on Iron Golem structure
- Custom texture needs to be added manually
- Building behavior may occasionally place blocks in unexpected locations

## Future Enhancements
- Custom model matching the vanilla Warden appearance
- Sound effects for building and jumping
- Particle effects for enhanced visual feedback
- Configuration file for adjusting AI behavior parameters

## Credits
- Built using Minecraft Forge MDK
- Enhanced AI algorithms for intelligent mob behavior
- Custom pathfinding and building mechanics

Enjoy the challenge of facing an AI Warden that can think, build, and jump its way to victory!
